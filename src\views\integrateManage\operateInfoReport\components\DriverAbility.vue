<template>
  <div class="ability-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="司机能力信息"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen"></TimeScreen>
      </el-col>
    </el-row>
    <main class="ability-main">
      <div class="ability-chart"></div>
      <div class="ability-notice">
        <div class="ability-box">
          <div class="ability-title">能力指标说明</div>
          <ul class="ability-list">
            <li class="ability-item" v-for="item in abilityList" :key="item.id">
              <div class="item-color" :style="{ backgroundColor: item.color }"></div>
              <div class="item-title">{{ item.title }}</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="ability-table">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }" height="600">
          <el-table-column prop="plateNumber" label="司机姓名" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="准时率" align="center" sortable></el-table-column>
          <el-table-column prop="operatorName" label="自检率" align="center" sortable></el-table-column>
          <el-table-column prop="operatorName" label="出勤率" align="center" sortable></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        tableData: [],
        abilityList: [
          { id: 0, title: "准时收运率:司机按时完成收运任务的比率", color: "#3b82f6" },
          { id: 1, title: "安全自检率:司机完成车辆安全自检的比率", color: "#27bc6c" },
          { id: 2, title: "出勤率:司机按时出勤的比率", color: "#e7a711" },
        ],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .ability-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .time-screen {
    justify-content: flex-end;
  }
  .ability-main {
    display: flex;
    justify-content: space-between;
    .ability-table {
      flex: 1;
      overflow: hidden;
    }
    .ability-notice {
      margin: 0 16px;
      display: flex;
      align-items: flex-end;
      .ability-box {
        background-color: #f9fafb;
        border-radius: 10px;
        padding: 16px;
        padding-right: 40px;
        .ability-title {
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }
  .ability-list {
    .ability-item {
      display: flex;
      align-items: center;
      margin-top: 6px;
      .item-color {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        overflow: hidden;
      }
      .item-title {
        font-size: 14px;
        color: #333;
        margin-left: 10px;
      }
    }
  }
</style>
