<template>
  <div class="anomaly-container">
    <SubTitle title="当日司机行驶里程异常提醒"></SubTitle>
    <ul class="anomaly-list" v-if="anomalyList.length > 0">
      <li class="anomaly-item" :class="{ active: item.active }" v-for="(item, index) in anomalyList" :key="index">
        <div class="item-color"></div>
        <div class="item-name">李卫东</div>
        <div class="item-title">当日平均行驶里程较所在车队偏高</div>
        <div class="item-name">+42.5%</div>
        <div class="item-text">当日行驶里程48.6km</div>
        <div class="item-text">当日大型床位司机平均行驶里程48.6km</div>
      </li>
    </ul>
    <el-empty v-else :image-size="100" description="暂无数据"></el-empty>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  export default {
    components: {
      SubTitle,
    },
    data() {
      return {
        anomalyList: [{}, { active: true }, {}],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .anomaly-container {
    margin-top: 16px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .anomaly-list {
    height: 180px;
    overflow-y: auto;
  }
  .anomaly-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #e3f8f1;
    margin-top: 16px;
    padding-right: 40px;
    .item-color {
      width: 10px;
      height: 44px;
      background-color: #10b981;
      filter: blur(1px) brightness(110%);
    }
    .item-name {
      font-size: 28px;
      color: #10b981;
    }
    &.active {
      background-color: #fef2f2;
      .item-color {
        background-color: #ef4444;
      }
      .item-name {
        color: #ef4444;
      }
    }
  }
</style>
