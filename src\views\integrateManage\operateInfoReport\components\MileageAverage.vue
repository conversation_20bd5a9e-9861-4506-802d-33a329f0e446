<template>
  <div class="average-container">
    <SubTitle title="平均行驶里程统计"></SubTitle>
    <el-row>
      <el-col class="mt-10">
        <el-select v-model="typeValue" placeholder="请选择司机类型" class="w200">
          <el-option
            v-for="(item, index) in typeList"
            :key="index"
            :label="item"
            :value="index"
            clearable
            filterable
          ></el-option>
        </el-select>
      </el-col>
      <el-col class="mt-10">
        <TimeScreen></TimeScreen>
      </el-col>
    </el-row>
    <div class="chart-box">
      <div class="chart" id="average-chart"></div>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        typeList: ["全体司机", "大型床位司机", "小型床位司机", "小诊所司机"],
        typeValue: 0,
        chartInstance: null,
        resizeTimer: null,
      };
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      initChart() {
        const chartDom = document.getElementById("average-chart");
        this.chartInstance = echarts.init(chartDom);

        // 模拟数据，您可以根据实际需求替换
        const xAxisData = [
          "2020-05-19",
          "2020-05-20",
          "2020-05-21",
          "2020-05-22",
          "2020-05-23",
          "2020-05-24",
          "2020-05-25",
        ];

        const seriesData = [280, 300, 310, 290, 280, 300, 290];

        const option = {
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: true,
            data: xAxisData,
            axisLine: {
              lineStyle: {
                color: "#e6e6e6",
              },
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
              interval: 0, // 强制显示所有标签
              rotate: 0, // 标签旋转角度，如果标签过长可以设置为45
              margin: 8, // 标签与轴线的距离
            },
            axisTick: {
              show: true,
              length: 5,
              lineStyle: {
                color: "#e6e6e6",
              },
            },
          },
          yAxis: {
            type: "value",
            name: "里程 (km)",
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
            },
            min: 0,
            max: 350,
            interval: 50,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            splitLine: {
              lineStyle: {
                color: "#f0f0f0",
                type: "solid",
              },
            },
          },
          series: [
            {
              type: "line",
              data: seriesData,
              smooth: true,
              symbol: "circle",
              symbolSize: 6,
              lineStyle: {
                color: "#003366",
                width: 2,
              },
              itemStyle: {
                color: "#003366",
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(0, 51, 102, 0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(0, 51, 102, 0.05)",
                    },
                  ],
                },
              },
            },
          ],
          tooltip: {
            trigger: "axis",
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            borderColor: "transparent",
            textStyle: {
              color: "#fff",
            },
            formatter: function (params) {
              const data = params[0];
              return `${data.name}<br/>里程: ${data.value} km`;
            },
          },
        };

        this.chartInstance.setOption(option);
      },
      // 更新图表数据的方法
      updateChartData(newData) {
        if (this.chartInstance && newData) {
          const option = {
            xAxis: {
              data: newData.xAxisData || [],
            },
            series: [
              {
                data: newData.seriesData || [],
              },
            ],
          };
          this.chartInstance.setOption(option);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .w200 {
    width: 200px;
  }
  .mt-12 {
    margin-top: 10px;
  }
  .average-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .chart-box {
    width: 100%;
    height: 320px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
