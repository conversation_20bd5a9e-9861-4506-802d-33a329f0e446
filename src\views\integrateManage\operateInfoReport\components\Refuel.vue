<template>
  <div class="refuel-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="加油数据统计"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen"></TimeScreen>
      </el-col>
    </el-row>
    <main class="refuel-main">
      <div class="refuel-chart">
        <div class="chart" id="refuel-chart" ref="refuelChart"></div>
      </div>
      <div class="refuel-table">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }">
          <el-table-column prop="driverType" label="司机类型" align="center"></el-table-column>
          <el-table-column prop="fuelAmount" label="加油量(升)" align="center"></el-table-column>
          <el-table-column prop="fuelCost" label="加油费用(元)" align="center"></el-table-column>
          <el-table-column prop="fuelFrequency" label="加油频次" align="center"></el-table-column>
          <el-table-column prop="avgCost" label="平均每次加油费用(元)" align="center"></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";

  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        chartInstance: null,
        tableData: [
          {
            driverType: "全职司机",
            fuelAmount: "12000",
            fuelCost: "102400",
            fuelFrequency: "12",
            avgCost: "12000",
          },
          {
            driverType: "大型车辆",
            fuelAmount: "42000",
            fuelCost: "12800",
            fuelFrequency: "58",
            avgCost: "12800",
          },
          {
            driverType: "小型车辆",
            fuelAmount: "32000",
            fuelCost: "",
            fuelFrequency: "42",
            avgCost: "",
          },
          {
            driverType: "小货车司机",
            fuelAmount: "16000",
            fuelCost: "",
            fuelFrequency: "25",
            avgCost: "",
          },
          {
            driverType: "普通司机",
            fuelAmount: "8000",
            fuelCost: "",
            fuelFrequency: "8",
            avgCost: "",
          },
        ],
        chartData: {
          categories: ["全职司机", "大型车辆", "小型车辆", "小货车司机", "普通司机"],
          fuelAmount: [12000, 42000, 32000, 16000, 8000], // 加油量(升)
          fuelCost: [102400, 12800, 0, 0, 0], // 加油费用(元)
          fuelFrequency: [120, 58, 42, 25, 8], // 加油频次
        },
      };
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      initChart() {
        const chartDom = document.getElementById("refuel-chart");
        this.chartInstance = echarts.init(chartDom);

        const option = {
          color: ["#3b82f6", "#10b981", "#f59e0b"],
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
            formatter: function (params) {
              let result = params[0].name + "<br/>";
              params.forEach((param) => {
                if (param.seriesName === "加油量(升)") {
                  result += `${param.marker}${param.seriesName}: ${param.value}升<br/>`;
                } else if (param.seriesName === "加油费用(元)") {
                  result += `${param.marker}${param.seriesName}: ${param.value}元<br/>`;
                } else if (param.seriesName === "加油频次") {
                  result += `${param.marker}${param.seriesName}: ${param.value}次<br/>`;
                }
              });
              return result;
            },
          },
          legend: {
            data: ["加油量(升)", "加油费用(元)", "加油频次"],
            top: "5%",
            left: "center",
            itemWidth: 14,
            itemHeight: 14,
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            top: "15%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.chartData.categories,
              axisPointer: {
                type: "shadow",
              },
              axisLabel: {
                interval: 0,
                rotate: 0,
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "数量",
              position: "left",
              axisLabel: {
                formatter: "{value}",
              },
            },
            {
              type: "value",
              name: "频次",
              position: "right",
              axisLabel: {
                formatter: "{value}",
              },
            },
          ],
          series: [
            {
              name: "加油量(升)",
              type: "bar",
              yAxisIndex: 0,
              data: this.chartData.fuelAmount,
              barWidth: "20%",
              itemStyle: {
                color: "#3b82f6",
              },
            },
            {
              name: "加油费用(元)",
              type: "bar",
              yAxisIndex: 0,
              data: this.chartData.fuelCost,
              barWidth: "20%",
              itemStyle: {
                color: "#70AD 47",
              },
            },
            {
              name: "加油频次",
              type: "line",
              yAxisIndex: 1,
              data: this.chartData.fuelFrequency,
              lineStyle: {
                color: "#FFC000",
                width: 3,
              },
              itemStyle: {
                color: "#fff",
                borderColor: "#FFC000",
                borderWidth: 2,
              },
              symbol: "circle",
              symbolSize: 8,
            },
          ],
        };

        this.chartInstance.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .refuel-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .time-screen {
    justify-content: flex-end;
  }
  .refuel-chart {
    width: 100%;
    height: 500px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
