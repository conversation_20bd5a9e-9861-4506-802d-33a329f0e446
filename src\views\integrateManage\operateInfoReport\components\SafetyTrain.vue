<template>
  <div class="safety-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="安全培训统计"></SubTitle>
      </el-col>
      <el-col :xl="12" class="time-screen">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          :clearable="false"
          @change="handleChange"
        ></el-date-picker>
      </el-col>
    </el-row>
    <main class="safety-main">
      <div class="safety-main-left">
        <div class="chart" id="train-chart"></div>
      </div>
      <div class="safety-main-right">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }" max-height="500">
          <el-table-column prop="plateNumber" label="季度" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="培训次数" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="参与人数" align="center"></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import moment from "moment";
  import * as echarts from "echarts";
  export default {
    components: {
      SubTitle,
    },
    data() {
      return {
        tableData: [],
        year: moment().format("YYYY"),
        chartInstance: null,
        chartData: [
          { name: "第一季度", value: 12, participantCount: 320 },
          { name: "第二季度", value: 8, participantCount: 280 },
          { name: "第三季度", value: 15, participantCount: 450 },
          { name: "第四季度", value: 10, participantCount: 380 },
        ],
      };
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      initChart() {
        const chartDom = document.getElementById("train-chart");
        this.chartInstance = echarts.init(chartDom);
        const chartData = this.chartData;
        const option = {
          color: ["#3b82f6", "#10b981", "#f59e0b", "#ef4444"],
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              const data = chartData.find((item) => item.name === params.name);
              return `${params.name}<br/><br/>培训次数: ${data.value}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;参与人数: ${data.participantCount}`;
            },
          },
          legend: {
            top: "5%",
            left: "center",
          },
          series: [
            {
              type: "pie",
              radius: ["40%", "70%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: "#fff",
                borderWidth: 2,
              },
              label: {
                show: false,
                position: "center",
              },
              // emphasis: {
              //   label: {
              //     show: true,
              //     fontSize: 40,
              //     fontWeight: "bold",
              //   },
              // },
              labelLine: {
                show: false,
              },
              data: this.chartData,
            },
          ],
        };
        this.chartInstance.setOption(option);
      },
      handleChange() {},
    },
  };
</script>

<style lang="scss" scoped>
  .safety-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
    margin-top: 20px;
  }
  .time-screen {
    display: flex;
    justify-content: flex-end;
  }
  .safety-main {
    display: flex;
    .safety-main-left {
      flex: 1;
      overflow: hidden;
      .chart {
        width: 100%;
        height: 500px;
      }
    }
    .safety-main-right {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
