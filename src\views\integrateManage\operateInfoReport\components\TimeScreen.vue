<template>
  <div class="is-flex micro-app-sctmp_base">
    <div class="mr-10">
      <el-date-picker
        v-model="timeForm.day"
        type="date"
        placeholder="请选择日期"
        value-format="yyyy-MM-dd"
        :clearable="false"
        v-if="radioValue === 0"
        @change="handleChange"
      ></el-date-picker>
      <el-date-picker
        v-model="timeForm.month"
        type="month"
        placeholder="请选择月份"
        :clearable="false"
        v-else-if="radioValue == 1"
        @change="handleChange"
      ></el-date-picker>
      <!-- 级联选择器示例 -->
      <el-cascader
        v-model="timeForm.quarter"
        :options="cascaderOptions"
        placeholder="请选择年份和季度"
        :clearable="false"
        v-else-if="radioValue == 2"
        @change="handleChange"
        class="w-220"
      ></el-cascader>
      <el-date-picker
        v-model="timeForm.year"
        type="year"
        placeholder="请选择年份"
        :clearable="false"
        @change="handleChange"
        v-else
      ></el-date-picker>
    </div>
    <el-radio-group v-model="radioValue" size="medium" @change="handleChange">
      <el-radio-button :label="0">日</el-radio-button>
      <el-radio-button :label="1">月</el-radio-button>
      <el-radio-button :label="2">季</el-radio-button>
      <el-radio-button :label="3">年</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
  import moment from "moment";
  export default {
    data() {
      return {
        radioValue: 0,
        timeForm: {
          year: moment().format("YYYY"),
          month: moment().format("YYYY-MM"),
          day: moment().format("YYYY-MM-DD"),
          quarter: [],
        },
        // 级联选择器数据
        cascaderOptions: [],
      };
    },
    created() {
      // 初始化级联选择器数据
      this.cascaderOptions = this.generateCascaderData();
      this.timeForm.quarter = [
        this.cascaderOptions[this.cascaderOptions.length - 1].value,
        this.cascaderOptions[this.cascaderOptions.length - 1].children[
          this.cascaderOptions[this.cascaderOptions.length - 1].children.length - 1
        ].value,
      ];
    },
    mounted() {
      this.emitData();
    },
    methods: {
      // 初始化数据
      emitData() {
        let form = {};
        switch (this.radioValue) {
          case 0:
            form.year = Number(this.timeForm.day.split("-")[0]);
            form.month = Number(this.timeForm.day.split("-")[1]);
            form.day = Number(this.timeForm.day.split("-")[2]);
            break;
          case 1:
            form.year = Number(this.timeForm.month.split("-")[0]);
            form.month = Number(this.timeForm.month.split("-")[1]);
            break;
          case 2:
            form.year = this.timeForm.quarter[0];
            form.quarter = this.timeForm.quarter[1];
            break;
          case 3:
            form.year = Number(this.timeForm.year);
            break;
        }
        this.$emit("change", form);
      },
      /**
       * 生成级联选择器数据
       * 从2020年开始至今，每个年份包含季度数据
       * 当前年份根据当前月份判断显示几个季度
       * @returns {Array} 级联选择器数据
       */
      generateCascaderData() {
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1; // getMonth()返回0-11，需要+1
        const startYear = 2020;
        const cascaderData = [];
        // 季度配置
        const quarters = [
          { label: "第一季度", value: 0, months: [1, 2, 3] },
          { label: "第二季度", value: 1, months: [4, 5, 6] },
          { label: "第三季度", value: 2, months: [7, 8, 9] },
          { label: "第四季度", value: 3, months: [10, 11, 12] },
        ];
        // 生成年份数据
        for (let year = startYear; year <= currentYear; year++) {
          const yearData = {
            label: `${year}年`,
            value: year,
            children: [],
          };
          if (year === currentYear) {
            // 当前年份：根据当前月份生成季度
            yearData.children = this.getCurrentYearQuarters(currentMonth, quarters);
          } else {
            // 其他年份：生成全部四个季度
            yearData.children = quarters.map((quarter) => ({
              label: quarter.label,
              value: quarter.value,
            }));
          }
          cascaderData.push(yearData);
        }
        return cascaderData;
      },
      getCurrentYearQuarters(currentMonth, quarters) {
        const availableQuarters = [];
        for (let i = 0; i < quarters.length; i++) {
          const quarter = quarters[i];
          // 如果当前月份大于等于该季度的第一个月，则该季度可用
          if (currentMonth >= quarter.months[0]) {
            availableQuarters.push({
              label: quarter.label,
              value: quarter.value,
            });
          }
        }
        return availableQuarters;
      },
      handleChange() {
        this.emitData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .mr-10 {
    margin-right: 10px;
  }
  .w-220 {
    width: 220px;
  }
  ::v-deep .el-radio-button__inner:hover {
    color: #003366;
  }
  ::v-deep .is-active .el-radio-button__inner:hover {
    color: #fff;
  }
  ::v-deep .is-active .el-radio-button__inner {
    background-color: #003366;
    border-color: #003366;
  }
</style>
